import { useState, useEffect } from 'react';
import { useNavigate } from '@tanstack/react-router';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '../lib/supabase';
import { useCertificate } from '../contexts/CertificateContext';

// Import the consolidated certificate status type
import type { CertificateStatus } from '../types/csv';

// Define certificate type
type CertificateType = 'WG/V' | 'WG/B' | 'NWG/V';

// Define certificate data structure
interface Certificate {
  id: string;
  created_at: string;
  updated_at: string;
  user_id: string;
  certificate_type: CertificateType | null;
  status: CertificateStatus;
  order_number: string | null;
  objektdaten?: Record<string, any> | null;
}

// Helper function to get certificate type display name
const getCertificateTypeName = (type: CertificateType | null): string => {
  switch (type) {
    case 'WG/V':
      return 'Wohngebäude-Verbrauchsausweis';
    case 'WG/B':
      return 'Wohngebä<PERSON>-Bedarfsausweis';
    case 'NWG/V':
      return 'Nicht-Wohngebäude-Verbrauchsausweis';
    default:
      return 'Unbekannter Typ';
  }
};


// Helper function to get status display name using consolidated status
const getStatusName = (status: CertificateStatus | null): string => {
  // Handle consolidated status values
  if (status === 'payment_complete') {
    return 'Bezahlt';
  }
  if (status === 'payment_expired') {
    return 'Zahlung abgelaufen';
  }
  if (status === 'payment_failed') {
    return 'Zahlung fehlgeschlagen';
  }
  if (status === 'payment_disputed') {
    return 'Zahlung bestritten';
  }
  if (status === 'payment_initiated') {
    return 'Zahlung eingeleitet';
  }


  // Certificate completion status based on page-based status system
  switch (status) {
    case 'zusammenfassung':
      return 'Bereit zur Zahlung';
    case 'verbrauch':
      return 'In Bearbeitung (Verbrauchsdaten)';
    case 'tww-lueftung':
      return 'In Bearbeitung (Trinkwarmwasser/Lüftung)';
    case 'heizung':
      return 'In Bearbeitung (Heizung)';
    case 'fenster':
      return 'In Bearbeitung (Fenster)';
    case 'gebaeudedetails2':
      return 'In Bearbeitung (Gebäudedetails)';
    case 'gebaeudedetails1':
      return 'In Bearbeitung (Gebäudedetails)';
    case 'objektdaten':
      return 'In Bearbeitung (Objektdaten)';
    default:
      return 'Unbekannt';
  }
};

export const MeineZertifikatePage = () => {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [showConfirmDelete, setShowConfirmDelete] = useState<string | null>(null);
  const { setActiveCertificateId } = useCertificate();

  // SECURITY FIX: User ID state for query key specificity
  const [userId, setUserId] = useState<string | null>(null);

  // SECURITY FIX: Fetch user ID on mount
  useEffect(() => {
    const getCurrentUser = async () => {
      const { data: user } = await supabase.auth.getUser();
      setUserId(user.user?.id || null);
    };
    getCurrentUser();
  }, []);

  // Clear success message after 3 seconds
  useEffect(() => {
    if (success) {
      const timer = setTimeout(() => {
        setSuccess(null);
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [success]);

  // SECURITY FIX: User-specific query with user ID in key
  const { data: certificates, isLoading, refetch } = useQuery({
    queryKey: ["energieausweise", "user", userId], // User-specific key
    queryFn: async () => {
      const { data: user } = await supabase.auth.getUser();
      if (!user.user) throw new Error('Nicht eingeloggt');

      const { data, error } = await supabase
        .from('energieausweise')
        .select('*')
        .eq('user_id', user.user.id)
        .order('updated_at', { ascending: false });

      if (error) throw error;
      return data as Certificate[];
    },
    enabled: !!userId, // SECURITY FIX: Only run when user ID is available
  });

  // Delete certificate mutation
  const deleteMutation = useMutation({
    mutationFn: async (certificateId: string) => {
      console.log('Deleting certificate with ID:', certificateId);

      // Get the current user
      const { data: user } = await supabase.auth.getUser();
      if (!user.user) throw new Error('Nicht eingeloggt');

      // Delete the certificate
      const { error, count } = await supabase
        .from('energieausweise')
        .delete()
        .eq('id', certificateId)
        .eq('user_id', user.user.id); // Ensure we're only deleting the user's own certificates

      if (error) throw error;

      console.log(`Deleted ${count} certificate(s)`);
      return certificateId;
    },
    onSuccess: async (deletedId) => {
      console.log('Certificate deleted successfully:', deletedId);

      // SECURITY FIX: Update cache with user-specific query key
      if (certificates && userId) {
        const updatedCertificates = certificates.filter(cert => cert.id !== deletedId);
        queryClient.setQueryData(["energieausweise", "user", userId], updatedCertificates);
      }

      // Also refetch to ensure we have the latest data
      await refetch();

      // Show success message
      setSuccess('Energieausweis wurde erfolgreich gelöscht');

      // Clear any previous error
      setError(null);

      // Close the confirmation dialog
      setShowConfirmDelete(null);
    },
    onError: (error) => {
      console.error('Error deleting certificate:', error);
      setError(`Fehler beim Löschen: ${error.message}`);
      setShowConfirmDelete(null); // Close the dialog even on error
    },
  });

  // Handle certificate deletion
  const handleDelete = (certificateId: string) => {
    setShowConfirmDelete(certificateId);
  };

  // Confirm certificate deletion
  const confirmDelete = (certificateId: string) => {
    if (certificateId) {
      try {
        deleteMutation.mutate(certificateId);
      } catch (err) {
        console.error('Error in delete mutation:', err);
        setError(`Fehler beim Löschen: ${err instanceof Error ? err.message : 'Unbekannter Fehler'}`);
      }
    } else {
      console.error('No certificate ID provided for deletion');
      setError('Keine Zertifikat-ID zum Löschen angegeben');
    }
  };

  // Handle creating a new certificate
  const handleCreateNew = () => {
    navigate({ to: '/' });
  };

  // Handle continuing with an existing certificate
  const handleContinue = async (certificateId: string) => {
    try {
      // Set the active certificate ID in the context
      setActiveCertificateId(certificateId);

      // SECURITY FIX: Invalidate user-specific queries to ensure fresh data is loaded
      if (userId) {
        queryClient.invalidateQueries({ queryKey: ["energieausweise", "user", userId] });
      }

      // Fetch the certificate's current status to navigate to the correct page
      const { data, error } = await supabase
        .from('energieausweise')
        .select('status, certificate_type')
        .eq('id', certificateId)
        .single();

      if (error) {
        console.error('Error fetching certificate status:', error);
        // Fallback to first page if there's an error
        navigate({ to: '/erfassen/objektdaten' });
        return;
      }

      // Navigate to the appropriate page based on the stored status
      const status = data.status;
      const certificateType = data.certificate_type;

      if (status) {
        // Check if this is a payment-related status
        const paymentStatuses = ['payment_initiated', 'payment_complete', 'payment_failed', 'payment_disputed', 'payment_expired'];
        if (paymentStatuses.includes(status)) {
          // For any payment-related status, navigate to summary page
          // since all data entry has been completed
          navigate({ to: '/erfassen/zusammenfassung' });
          return;
        }

        // Navigate to the page corresponding to the stored status
        const pageRoutes: Record<string, string> = {
          'objektdaten': '/erfassen/objektdaten',
          'gebaeudedetails1': '/erfassen/gebaeudedetails1',
          'gebaeudedetails2': '/erfassen/gebaeudedetails2',
          'gebaeudeform': '/erfassen/gebaeudeform',
          'fenster': '/erfassen/fenster',
          'heizung': '/erfassen/heizung',
          'tww-lueftung': '/erfassen/tww-lueftung',
          'verbrauch': '/erfassen/verbrauch',
          'zusammenfassung': '/erfassen/zusammenfassung'
        };

        const targetRoute = pageRoutes[status];
        if (targetRoute) {
          // Check if the route is valid for the certificate type
          const isValidRoute = (route: string, certType: string) => {
            if (route === '/erfassen/gebaeudeform' || route === '/erfassen/fenster' || route === '/erfassen/heizung') {
              return certType === 'WG/B';
            }
            if (route === '/erfassen/tww-lueftung') {
              return certType === 'WG/B';
            }
            if (route === '/erfassen/verbrauch') {
              return certType === 'WG/V' || certType === 'NWG/V';
            }
            return true; // Other routes are valid for all types
          };

          if (certificateType && isValidRoute(targetRoute, certificateType)) {
            navigate({ to: targetRoute });
          } else {
            // If the stored status is not valid for the certificate type,
            // navigate to the appropriate page for that type
            if (certificateType === 'WG/B' && status === 'verbrauch') {
              navigate({ to: '/erfassen/zusammenfassung' });
            } else {
              navigate({ to: '/erfassen/objektdaten' });
            }
          }
        } else {
          // Unknown status, fallback to first page
          navigate({ to: '/erfassen/objektdaten' });
        }
      } else {
        // No status, start from the beginning
        navigate({ to: '/erfassen/objektdaten' });
      }
    } catch (error) {
      console.error('Error in handleContinue:', error);
      // Fallback to first page if there's any error
      navigate({ to: '/erfassen/objektdaten' });
    }
  };

  return (
    <div className="max-w-6xl mx-auto">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold text-gray-800">
          Meine Energieausweise
        </h1>
        <button
          onClick={handleCreateNew}
          className="bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-lg transition-colors"
        >
          Neuen Energieausweis erstellen
        </button>
      </div>

      {error && (
        <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-500" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </div>
        </div>
      )}

      {success && (
        <div className="bg-green-50 border-l-4 border-green-500 p-4 mb-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-green-500" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-green-700">{success}</p>
            </div>
          </div>
        </div>
      )}

      {isLoading ? (
        <div className="text-center py-8">
          <div className="w-12 h-12 border-t-4 border-green-500 border-solid rounded-full animate-spin mx-auto"></div>
          <p className="mt-4 text-gray-600">Energieausweise werden geladen...</p>
        </div>
      ) : certificates && certificates.length > 0 ? (
        <div className="bg-white rounded-lg shadow-md overflow-hidden">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Typ
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Bestellnummer
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Zuletzt bearbeitet
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Aktionen
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {certificates.map((certificate) => (
                <tr key={certificate.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">
                      {getCertificateTypeName(certificate.certificate_type)}
                    </div>
                    <div className="text-sm text-gray-500">
                      {certificate.objektdaten?.Strasse && certificate.objektdaten?.Hausnr 
                        && certificate.objektdaten?.PLZ && certificate.objektdaten?.Ort
                        ? `${certificate.objektdaten.Strasse} ${certificate.objektdaten.Hausnr} ${certificate.objektdaten.PLZ} ${certificate.objektdaten.Ort} `
                        : 'Keine Adresse'}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {certificate.order_number || '-'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {new Date(certificate.updated_at).toLocaleString("de-DE", {
                      day: "2-digit",
                      month: "2-digit",
                      year: "numeric",
                      hour: "2-digit",
                      minute: "2-digit",
                    })}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                      ${certificate.status === 'payment_complete'
                        ? 'bg-green-100 text-green-800'
                        : certificate.status === 'payment_failed' || certificate.status === 'payment_expired' || certificate.status === 'payment_disputed'
                          ? 'bg-red-100 text-red-800'
                          : certificate.status === 'zusammenfassung' || certificate.status === 'payment_initiated'
                            ? 'bg-blue-100 text-blue-800'
                            : 'bg-yellow-100 text-yellow-800'}`}>
                      {getStatusName(certificate.status)}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <button
                      onClick={() => handleContinue(certificate.id)}
                      className="text-green-600 hover:text-green-900 mr-4"
                    >
                      Bearbeiten
                    </button>
                    <button
                      onClick={() => handleDelete(certificate.id)}
                      className="text-red-600 hover:text-red-900"
                    >
                      Löschen
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      ) : (
        <div className="bg-white rounded-lg shadow-md p-8 text-center">
          <svg className="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Keine Energieausweise vorhanden</h3>
          <p className="text-gray-600 mb-6">
            Sie haben noch keine Energieausweise erstellt. Klicken Sie auf den Button, um Ihren ersten Energieausweis zu erstellen.
          </p>
          <button
            onClick={handleCreateNew}
            className="bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-6 rounded-lg transition-colors"
          >
            Energieausweis erstellen
          </button>
        </div>
      )}

      {/* Confirmation Dialog */}
      {showConfirmDelete && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Energieausweis löschen</h3>
            <p className="text-gray-600 mb-6">
              Sind Sie sicher, dass Sie diesen Energieausweis löschen möchten? Diese Aktion kann nicht rückgängig gemacht werden.
            </p>
            <div className="flex justify-end space-x-4">
              <button
                onClick={() => setShowConfirmDelete(null)}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                type="button"
              >
                Abbrechen
              </button>
              <button
                onClick={() => {
                  console.log('Delete button clicked for certificate:', showConfirmDelete);
                  confirmDelete(showConfirmDelete);
                }}
                className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 flex items-center"
                type="button"
                disabled={deleteMutation.isPending}
              >
                {deleteMutation.isPending ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Wird gelöscht...
                  </>
                ) : (
                  'Löschen'
                )}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
