import { useState, useEffect, useCallback } from "react";
import { useForm, useField } from "@tanstack/react-form";
import { useMutation, useQueryClient, useQuery } from "@tanstack/react-query";
import { z } from "zod";
import { Link, useNavigate } from "@tanstack/react-router";
import { supabase } from "../../lib/supabase";
import { useCertificate } from "../../contexts/CertificateContext";
import { Breadcrumb } from "../../components/ui/Breadcrumb";
import { usePageVisit } from "../../hooks/usePageVisit";
import { useNavigationState } from "../../hooks/useNavigationState";
import { useCertificateType } from "../../hooks/useCertificateType";
import {
  buildingShapes,
  getBuildingShapeById,
  type BuildingShape,
  type WallTemplate,
} from "../../config/buildingShapes";

// Define the Bauteil schema for reuse (copied from GebaeudedetailsPage2)
const bauteilSchema = z.object({
  id: z.string(),
  bezeichnung: z.string().min(1, "Bezeichnung ist erforderlich"),
  massiv: z.string().optional(),
  uebergang: z.string().optional(),
  flaeche: z.string().min(1, "Fläche ist erforderlich"),
  uWert: z.string().optional(),
  // Intermediate calculation fields for wall area calculator
  wallWidth: z.string().optional(),
  numberOfLevels: z.string().optional(),
});

// Define the form schema
const gebaeudeformSchema = z.object({
  buildingShape: z.string().min(1, "Gebäudeform ist erforderlich"),
  waende: z.array(bauteilSchema).default([]),
});

type GebaeudeformFormValues = z.infer<typeof gebaeudeformSchema>;
type Bauteil = z.infer<typeof bauteilSchema>;

// Building Shape Card Component
const BuildingShapeCard = ({
  shape,
  selected,
  onSelect,
}: {
  shape: BuildingShape;
  selected: boolean;
  onSelect: () => void;
}) => {
  return (
    <div
      className={`border-2 rounded-lg p-4 cursor-pointer transition-all ${
        selected
          ? "border-blue-500 bg-blue-50"
          : "border-gray-200 hover:border-gray-300 hover:bg-gray-50"
      }`}
      onClick={onSelect}
    >
      <div className="flex items-center mb-3">
        <input
          type="radio"
          checked={selected}
          onChange={onSelect}
          className="mr-3 h-4 w-4 text-blue-600"
        />
        <h3 className="text-lg font-semibold text-gray-800">{shape.name}</h3>
      </div>

      <div className="mb-3">
        <img
          src={shape.image}
          alt={shape.name}
          className="w-full h-32 object-contain bg-gray-100 rounded"
          onError={(e) => {
            // Fallback for missing images
            const target = e.target as HTMLImageElement;
            target.style.display = "none";
            const placeholder = target.nextElementSibling as HTMLElement | null;
            if (placeholder) {
              placeholder.classList.remove("hidden");
              placeholder.classList.add("flex");
            }
          }}
        />
        <div className="hidden w-full h-32 bg-gray-100 rounded items-center justify-center text-gray-500">
          <span>Bild wird geladen...</span>
        </div>
      </div>

      <p className="text-sm text-gray-600 mb-2">{shape.description}</p>
      <p className="text-xs text-gray-500">
        {shape.walls.length} Außenwände:{" "}
        {shape.walls.map((w) => w.bezeichnung).join(", ")}
      </p>
    </div>
  );
};

// Wall Area Calculator Component - Restored from original implementation
const WallAreaCalculator = ({
  flaecheField,
  wallWidthField,
  index,
  existingData,
}: {
  flaecheField: any;
  wallWidthField: any;
  index: number;
  existingData: any;
}) => {
  const [calculatedArea, setCalculatedArea] = useState<string>("");

  // Get previously entered values from gebaeudedetails1
  const gebaeudedetails1Data = existingData?.gebaeudedetails1 as any;
  const geschosseValue = gebaeudedetails1Data?.Geschosse || "";
  const raumhoeheValue = gebaeudedetails1Data?.Raumhöhe || "";

  // Calculate area when width, geschosseValue, or raumhoeheValue changes
  useEffect(() => {
    const wallWidth = wallWidthField.state.value || "";

    const width = parseFloat(wallWidth.replace(",", "."));
    const levels = parseFloat(geschosseValue);
    const roomHeight = parseFloat(raumhoeheValue.replace(",", "."));

    if (
      !isNaN(width) &&
      !isNaN(levels) &&
      !isNaN(roomHeight) &&
      width > 0 &&
      levels > 0 &&
      roomHeight > 0
    ) {
      const area = width * levels * roomHeight;
      const formattedArea = area.toFixed(2).replace(".", ",");
      setCalculatedArea(formattedArea);
      // Update the form field with calculated value
      flaecheField.handleChange(formattedArea);
    } else {
      setCalculatedArea("");
      // Clear the form field if calculation is invalid
      flaecheField.handleChange("");
    }
  }, [
    wallWidthField.state.value,
    geschosseValue,
    raumhoeheValue,
    flaecheField,
  ]);

  return (
    <div className="mb-2">
      <label className="block text-sm font-medium text-gray-700 mb-2">
        Wandflächenberechnung <span className="text-red-500">*</span>
      </label>

      {/* Display previously entered values as read-only information */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mb-3 p-3 bg-gray-50 rounded-md">
        <div>
          <label className="block text-xs font-medium text-gray-600 mb-1">
            Anzahl Geschosse (aus Gebäudedaten)
          </label>
          <div className="text-sm text-gray-700 font-medium">
            {geschosseValue || "Nicht verfügbar"}
          </div>
        </div>
        <div>
          <label className="block text-xs font-medium text-gray-600 mb-1">
            Raumhöhe (aus Gebäudedaten)
          </label>
          <div className="text-sm text-gray-700 font-medium">
            {raumhoeheValue ? `${raumhoeheValue} m` : "Nicht verfügbar"}
          </div>
        </div>
      </div>

      {/* User input for wall width */}
      <div className="mb-3">
        <label
          htmlFor={`wand-${index}-width`}
          className="block text-sm font-medium text-gray-700 mb-1"
        >
          Länge der Wand (m) <span className="text-red-500">*</span>
        </label>
        <input
          id={`wand-${index}-width`}
          type="text"
          value={wallWidthField.state.value || ""}
          onChange={(e) => wallWidthField.handleChange(e.target.value)}
          onBlur={wallWidthField.handleBlur}
          className="w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 border-gray-300"
          placeholder="z.B. 12,5"
        />
      </div>

      {/* Calculated result */}
      <div className="mb-2">
        <label
          htmlFor={`wand-${index}-flaeche`}
          className="block text-sm font-medium text-gray-700 mb-1"
        >
          Berechnete Fläche in m²
        </label>
        <input
          id={`wand-${index}-flaeche`}
          type="text"
          value={calculatedArea}
          readOnly
          className="w-full px-3 py-2 border rounded-md shadow-sm bg-gray-50 text-gray-700 border-gray-300"
          placeholder="Wird automatisch berechnet"
        />
        {calculatedArea && geschosseValue && raumhoeheValue && (
          <p className="mt-1 text-xs text-gray-500">
            Berechnung: {(wallWidthField.state.value || "").replace(",", ".")} m
            × {geschosseValue} Geschosse × {raumhoeheValue} m = {calculatedArea}{" "}
            m²
          </p>
        )}
        {(!geschosseValue || !raumhoeheValue) && (
          <p className="mt-1 text-xs text-amber-600">
            Hinweis: Geschosse und Raumhöhe müssen in den Gebäudedaten (Teil 1)
            eingegeben werden.
          </p>
        )}
      </div>

      {flaecheField.state.meta.errors.length > 0 && (
        <p className="mt-1 text-sm text-red-500">
          {flaecheField.state.meta.errors.join(", ")}
        </p>
      )}
    </div>
  );
};

// Wall Field Component - Restored from original BauteilField implementation
const WallField = ({
  index,
  form,
  existingData,
  onRemove,
  canRemove,
}: {
  index: number;
  form: any;
  existingData: any;
  onRemove: () => void;
  canRemove: boolean;
}) => {
  // Use Tanstack Form's useField for each field
  const bezeichnungField = useField({
    name: `waende[${index}].bezeichnung` as const,
    form,
  });

  const massivField = useField({
    name: `waende[${index}].massiv` as const,
    form,
  });

  const flaecheField = useField({
    name: `waende[${index}].flaeche` as const,
    form,
  });

  // Intermediate calculation field for wall area calculator
  const wallWidthField = useField({
    name: `waende[${index}].wallWidth` as const,
    form,
  });

  return (
    <div className="p-4 mb-4 border rounded-md bg-gray-50">
      <div className="flex justify-between mb-2">
        <h4 className="font-medium">Außenwand {index + 1}</h4>
        {canRemove && (
          <button
            type="button"
            onClick={onRemove}
            className="text-red-500 hover:text-red-700"
          >
            Entfernen
          </button>
        )}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="mb-2">
          <label
            htmlFor={`wand-${index}-bezeichnung`}
            className="block text-sm font-medium text-gray-700 mb-1"
          >
            Bezeichnung <span className="text-red-500">*</span>
          </label>
          <input
            id={`wand-${index}-bezeichnung`}
            type="text"
            value={String(bezeichnungField.state.value ?? "")}
            onChange={(e) => bezeichnungField.handleChange(e.target.value)}
            onBlur={bezeichnungField.handleBlur}
            className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 ${
              bezeichnungField.state.meta.errors.length > 0
                ? "border-red-500"
                : "border-gray-300"
            }`}
            placeholder="z.B. Außenwand"
          />
          {bezeichnungField.state.meta.errors.length > 0 && (
            <p className="mt-1 text-sm text-red-500">
              {bezeichnungField.state.meta.errors.join(", ")}
            </p>
          )}
        </div>

        <div className="mb-2">
          <label
            htmlFor={`wand-${index}-massiv`}
            className="block text-sm font-medium text-gray-700 mb-1"
          >
            Material
          </label>
          <select
            id={`wand-${index}-massiv`}
            value={String(massivField.state.value ?? "")}
            onChange={(e) => massivField.handleChange(e.target.value)}
            onBlur={massivField.handleBlur}
            className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 ${
              massivField.state.meta.errors.length > 0
                ? "border-red-500"
                : "border-gray-300"
            }`}
          >
            <option value="kb_zweischaligOhneDaemm">
              Massiv, Zweischalig ohne Dämmung
            </option>
            <option value="kb_Massiv">
              Ziegel/Hohlstein (massive Konstruktion)
            </option>
            <option value="kb_Holz">Holzkonstruktion</option>
            <option value="kb_Stahlbeton">Stahlbeton</option>
            <option value="kb_zweischaligMitDaemm">
              Zweischalig mit Dämmung
            </option>
            <option value="kb_MassivBis20">
              Massivwand bis 20 cm Vollziegel, Naturstein, KS
            </option>
            <option value="kb_Massiv20bis30">
              Massivwand 20-30 cm Vollziegel,Naturstein, KS
            </option>
            <option value="kb_Massivueber30">
              Massivwand über 30 cm Vollziegel, Naturstein, KS
            </option>
            <option value="kb_sonstMassbis20">
              sonst.Massivwand bis 20 cm{" "}
            </option>
            <option value="kb_sonstMassuebber20">
              sonst. Massivwand über 20 cm
            </option>
            <option value="kb_Massivholz">Holz massiv (Blockbohlen)</option>
            <option value="kb_FachwerkLehm">Fachwerk mit Lehmfachung</option>
            <option value="kb_FachwerkVollziegel">
              Fachwerk mit Vollziegelfachung
            </option>
            <option value="kb_Rollladen_gedaemmt">
              Rollladenkasten gedämmt
            </option>
            <option value="kb_Rollladen_ungedaemmt">
              Rollladenkasten ungedämmt
            </option>
          </select>
          {massivField.state.meta.errors.length > 0 && (
            <p className="mt-1 text-sm text-red-500">
              {massivField.state.meta.errors.join(", ")}
            </p>
          )}
        </div>

        {/* Wall area calculation - simplified to only require wall width */}
        <WallAreaCalculator
          flaecheField={flaecheField}
          wallWidthField={wallWidthField}
          index={index}
          existingData={existingData}
        />
      </div>
    </div>
  );
};

export const GebaeudeformPage = () => {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [selectedShape, setSelectedShape] = useState<string | null>(null);
  const [waende, setWaende] = useState<Bauteil[]>([]);

  // Mark this page as visited for navigation tracking
  usePageVisit("gebaeudeform");
  const { activeCertificateId } = useCertificate();
  const { certificateType } = useCertificateType();
  const { markPageAsVisited } = useNavigationState(certificateType);

  // Fetch existing data
  const {
    data: existingData,
    isError,
    error,
  } = useQuery({
    queryKey: ["energieausweise", "gebaeudeform", activeCertificateId],
    queryFn: async () => {
      if (!activeCertificateId) return null;

      const { data, error } = await supabase
        .from("energieausweise")
        .select("gebaeudedetails2, gebaeudedetails1")
        .eq("id", activeCertificateId)
        .single();

      if (error) {
        throw error;
      }
      return data;
    },
    enabled: !!activeCertificateId,
    retry: false,
  });

  // Initial form values
  const initialValues: Partial<GebaeudeformFormValues> = {
    buildingShape: "",
    waende: [],
  };

  // Create the form
  const form = useForm({
    defaultValues: initialValues,
    onSubmit: async ({ value }) => {
      setSubmitError(null);

      try {
        // Validate the form data
        const validatedData = gebaeudeformSchema.parse(value);
        await saveMutation.mutateAsync(validatedData);
      } catch (error) {
        if (error instanceof z.ZodError) {
          setSubmitError("Bitte füllen Sie alle erforderlichen Felder aus.");
        } else {
          setSubmitError(`Fehler beim Speichern: ${(error as Error).message}`);
        }
      }
    },
  });

  // Generate walls based on selected building shape
  const generateWallsForShape = useCallback(
    (shapeId: string) => {
      const shape = getBuildingShapeById(shapeId);
      if (!shape) return [];

      // Get numberOfLevels from gebaeudedetails1 data
      const gebaeudedetails1Data = existingData?.gebaeudedetails1 as any;
      const numberOfLevels = gebaeudedetails1Data?.Geschosse || "";

      return shape.walls.map((wallTemplate: WallTemplate) => ({
        id: `${shapeId}-${wallTemplate.id}`,
        bezeichnung: wallTemplate.bezeichnung,
        massiv: wallTemplate.defaultMassiv,
        uebergang: "",
        flaeche: "",
        uWert: "",
        wallWidth: "",
        numberOfLevels: numberOfLevels,
      }));
    },
    [existingData],
  );

  // Handle building shape selection
  const handleShapeSelection = useCallback(
    (shapeId: string) => {
      setSelectedShape(shapeId);
      form.setFieldValue("buildingShape", shapeId);

      // Generate walls for the selected shape
      const newWalls = generateWallsForShape(shapeId);
      setWaende(newWalls);
      form.setFieldValue("waende", newWalls);
    },
    [form, generateWallsForShape],
  );

  // Load existing data when component mounts
  useEffect(() => {
    if (existingData?.gebaeudedetails2) {
      const data = existingData.gebaeudedetails2 as any;

      if (data.buildingShape) {
        setSelectedShape(data.buildingShape);
        form.setFieldValue("buildingShape", data.buildingShape);

        // Also load the walls for this shape
        if (data.waende && Array.isArray(data.waende)) {
          // Update existing walls to ensure numberOfLevels is populated from gebaeudedetails1
          const gebaeudedetails1Data = existingData.gebaeudedetails1 as any;
          const numberOfLevels = gebaeudedetails1Data?.Geschosse || "";

          const updatedWalls = data.waende.map((wall: Bauteil) => ({
            ...wall,
            numberOfLevels: wall.numberOfLevels || numberOfLevels,
          }));

          setWaende(updatedWalls);
          form.setFieldValue("waende", updatedWalls);
        } else {
          // Generate default walls if none exist
          const defaultWalls = generateWallsForShape(data.buildingShape);
          setWaende(defaultWalls);
          form.setFieldValue("waende", defaultWalls);
        }
      }
    }
  }, [existingData, form, generateWallsForShape]);

  // Update numberOfLevels in existing walls when gebaeudedetails1 data changes
  useEffect(() => {
    if (existingData?.gebaeudedetails1 && waende.length > 0) {
      const gebaeudedetails1Data = existingData.gebaeudedetails1 as any;
      const numberOfLevels = gebaeudedetails1Data?.Geschosse || "";

      // Check if any wall needs updating
      const needsUpdate = waende.some(
        (wall) => wall.numberOfLevels !== numberOfLevels,
      );

      if (needsUpdate) {
        const updatedWalls = waende.map((wall) => ({
          ...wall,
          numberOfLevels: numberOfLevels,
        }));

        setWaende(updatedWalls);
        form.setFieldValue("waende", updatedWalls);
      }
    }
  }, [existingData?.gebaeudedetails1, waende, form]);

  // Functions to add and remove walls
  const addWall = useCallback(() => {
    const newId = `${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

    // Get numberOfLevels from gebaeudedetails1 data
    const gebaeudedetails1Data = existingData?.gebaeudedetails1 as any;
    const numberOfLevels = gebaeudedetails1Data?.Geschosse || "";

    const newWall: Bauteil = {
      id: newId,
      bezeichnung: `Außenwand ${waende.length + 1}`,
      massiv: "kb_zweischaligOhneDaemm",
      uebergang: "",
      flaeche: "",
      uWert: "",
      wallWidth: "",
      numberOfLevels: numberOfLevels,
    };

    const updatedWalls = [...waende, newWall];
    setWaende(updatedWalls);
    form.setFieldValue("waende", updatedWalls);
  }, [waende, form, existingData]);

  const removeWall = useCallback(
    (index: number) => {
      const updatedWalls = waende.filter((_, i) => i !== index);
      setWaende(updatedWalls);
      form.setFieldValue("waende", updatedWalls);
    },
    [waende, form],
  );

  // Define the mutation for saving data to Supabase
  const saveMutation = useMutation({
    mutationFn: async (data: GebaeudeformFormValues) => {
      if (!activeCertificateId)
        throw new Error("Kein aktives Zertifikat ausgewählt.");

      // Get existing gebaeudedetails2 data
      const { data: existingData } = await supabase
        .from("energieausweise")
        .select("gebaeudedetails2")
        .eq("id", activeCertificateId)
        .single();

      // Merge with existing data
      const updatedGebaeudedetails2 = {
        ...((existingData?.gebaeudedetails2 as any) || {}),
        buildingShape: data.buildingShape,
        waende: data.waende,
      };

      const { data: result, error } = await supabase
        .from("energieausweise")
        .update({
          gebaeudedetails2: updatedGebaeudedetails2,
          updated_at: new Date().toISOString(),
        })
        .eq("id", activeCertificateId)
        .select()
        .single();

      if (error) throw error;
      return result;
    },
    onSuccess: async () => {
      // Invalidate queries to refresh data
      queryClient.invalidateQueries({
        queryKey: ["energieausweise", activeCertificateId],
      });
      queryClient.invalidateQueries({
        queryKey: ["energieausweise", "gebaeudeform", activeCertificateId],
      });
      // Invalidate the walls query used by FensterPage for synchronization
      queryClient.invalidateQueries({
        queryKey: [
          "energieausweise",
          "gebaeudeform_walls",
          activeCertificateId,
        ],
      });

      // Update navigation state to mark next page as current
      await markPageAsVisited("fenster");

      // Navigate to the next page (fenster for WG/B)
      navigate({ to: "/erfassen/fenster" });
    },
    onError: (error) => {
      setSubmitError(`Fehler beim Speichern: ${error.message}`);
    },
  });

  if (isError) {
    return (
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="bg-white shadow-md rounded-lg p-6">
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
            Fehler beim Laden der Daten: {error?.message}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
      {/* Breadcrumb Navigation */}
      <Breadcrumb className="mb-6" />

      <h1 className="text-3xl font-bold text-gray-800 mb-6">
        Gebäudeform und Außenwände
      </h1>
      <p className="text-lg text-gray-600 mb-8">
        Wählen Sie die Form Ihres Gebäudes aus und konfigurieren Sie die
        entsprechenden Außenwände.
      </p>

      <form
        onSubmit={(e) => {
          e.preventDefault();
          e.stopPropagation();
          form.handleSubmit();
        }}
        className="bg-white shadow-md rounded-lg p-6"
      >
        {/* Building Shape Selection */}
        <div className="mb-8">
          <h2 className="text-xl font-semibold text-gray-800 mb-4 pb-2 border-b">
            Gebäudeform auswählen
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {buildingShapes.map((shape) => (
              <BuildingShapeCard
                key={shape.id}
                shape={shape}
                selected={selectedShape === shape.id}
                onSelect={() => handleShapeSelection(shape.id)}
              />
            ))}
          </div>
        </div>

        {/* Wall Configuration Section - Only show if shape is selected */}
        {selectedShape && waende.length > 0 && (
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-gray-800 mb-4 pb-2 border-b">
              Außenwände konfigurieren
            </h2>
            <p className="text-sm text-gray-600 mb-4">
              Konfigurieren Sie die Außenwände für die gewählte Gebäudeform "
              {getBuildingShapeById(selectedShape)?.name}".
            </p>
            {waende.map((wand: Bauteil, index: number) => (
              <WallField
                key={wand.id}
                index={index}
                form={form}
                existingData={existingData}
                onRemove={() => removeWall(index)}
                canRemove={waende.length > 1}
              />
            ))}
            <div className="flex gap-2 mt-4">
              <button
                type="button"
                onClick={addWall}
                className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors"
              >
                Weitere Außenwand hinzufügen
              </button>
            </div>
          </div>
        )}

        {submitError && (
          <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
            {submitError}
          </div>
        )}

        {/* Navigation Buttons */}
        <div className="flex justify-between mt-8">
          <Link
            to="/erfassen/gebaeudedetails2"
            className="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300 transition-colors"
          >
            Zurück
          </Link>
          <button
            type="submit"
            disabled={!selectedShape || saveMutation.isPending}
            className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors disabled:bg-green-300"
          >
            {saveMutation.isPending ? "Speichern..." : "Weiter"}
          </button>
        </div>
      </form>
    </div>
  );
};
