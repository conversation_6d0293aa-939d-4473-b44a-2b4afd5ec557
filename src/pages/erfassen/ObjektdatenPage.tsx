import { useState, useEffect } from 'react';
import { useForm } from '@tanstack/react-form';
import { useMutation, useQueryClient, useQuery } from '@tanstack/react-query';
import { z } from 'zod';
import { Link, useNavigate } from '@tanstack/react-router';
import { supabase } from '../../lib/supabase';
import { useCertificate } from '../../contexts/CertificateContext';
import { useAuth } from '../../contexts/AuthContext';

import { DirectoryBasedFileUpload } from '../../components/ui/DirectoryBasedFileUpload';
import { Breadcrumb } from '../../components/ui/Breadcrumb';
import { usePageVisit } from '../../hooks/usePageVisit';
import { useNavigationState } from '../../hooks/useNavigationState';
import { EnhancedFormField } from '../../components/ui/EnhancedFormField';
import { ValidationErrorSummary } from '../../components/ui/ValidationErrorSummary';
import { handleZodValidationError, clearFormFieldErrors } from '../../utils/formValidation';
import { AnonymousUserIndicator } from '../../components/ui/AnonymousUserIndicator';

// Define the certificate types
type CertificateType = 'WG/V' | 'WG/B' | 'NWG/V';

// Define the form schema using Zod
const objektdatenSchema = z.object({
  // Objektdaten
  Strasse: z.string().min(1, 'Straße ist erforderlich'),
  Hausnr: z.string().min(1, 'Hausnummer ist erforderlich'),
  PLZ: z.string().length(5, 'PLZ muss 5 Ziffern haben').regex(/^\d+$/, 'PLZ muss aus Ziffern bestehen'),
  Ort: z.string().min(1, 'Ort ist erforderlich'),
  WSchVo77_erfuellt: z.string().optional(),

  // Kundendaten
  Kunden_Anrede: z.string().min(1, 'Anrede ist erforderlich'),
  Kunden_Vorname: z.string().min(1, 'Vorname ist erforderlich'),
  Kunden_Nachname: z.string().min(1, 'Nachname ist erforderlich'),
  Kunden_Strasse: z.string().min(1, 'Straße ist erforderlich'),
  Kunden_Hausnr: z.string().min(1, 'Hausnummer ist erforderlich'),
  Kunden_PLZ: z.string().length(5, 'PLZ muss 5 Ziffern haben').regex(/^\d+$/, 'PLZ muss aus Ziffern bestehen'),
  Kunden_Ort: z.string().min(1, 'Ort ist erforderlich'),
  Kunden_email: z.string().email('Gültige E-Mail-Adresse erforderlich'),
  Kunden_telefon: z.string().optional(),
});

type ObjektdatenFormValues = z.infer<typeof objektdatenSchema>;

export const ObjektdatenPage = () => {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [validationErrors, setValidationErrors] = useState<Record<string, string[]>>({});
  const [isLoading, setIsLoading] = useState(true);

  // Mark this page as visited for navigation tracking
  usePageVisit('objektdaten');

  const [certificateType, setCertificateType] = useState<CertificateType | null>(null);
  const { activeCertificateId } = useCertificate();
  const { isAnonymous } = useAuth();
  const { markPageAsVisited } = useNavigationState(certificateType);



  const initialValues: Partial<ObjektdatenFormValues> = {
    Strasse: '',
    Hausnr: '',
    PLZ: '',
    Ort: '',
    WSchVo77_erfuellt: '1', // Default to "Ja"
    Kunden_Anrede: '',
    Kunden_Vorname: '',
    Kunden_Nachname: '',
    Kunden_Strasse: '',
    Kunden_Hausnr: '',
    Kunden_PLZ: '',
    Kunden_Ort: '',
    Kunden_email: '',
    Kunden_telefon: '',
  };

  // Fetch certificate type
  const { data: certificateData } = useQuery({
    queryKey: ['energieausweise', 'certificate_type', activeCertificateId],
    queryFn: async () => {
      if (!activeCertificateId) return null;

      const { data, error } = await supabase
        .from('energieausweise')
        .select('certificate_type')
        .eq('id', activeCertificateId)
        .single();

      if (error) {
        throw error;
      }

      return data;
    },
    enabled: !!activeCertificateId,
    retry: false,
  });

  // Fetch existing objektdaten
  const { data: existingData, isError, error } = useQuery({
    queryKey: ['energieausweise', 'objektdaten', activeCertificateId],
    queryFn: async () => {
      if (!activeCertificateId) return null;

      // Use proper typing for the Supabase query builder
      const { data, error } = await supabase
        .from('energieausweise')
        .select('objektdaten')
        .eq('id', activeCertificateId)
        .single();

      if (error) {
        throw error;
      }

      // `data` will be an object like { objektdaten: YourData } or null if no row found
      return data;
    },
    enabled: !!activeCertificateId,
    retry: false,
  });

  // Update certificate type when data is fetched
  useEffect(() => {
    if (certificateData?.certificate_type) {
      setCertificateType(certificateData.certificate_type as CertificateType);
    }
  }, [certificateData]);

  useEffect(() => {
    // Set loading to false when queries have completed (regardless of whether they have data)
    if (!activeCertificateId) {
      setIsLoading(false);
      return;
    }

    // Check if both queries have completed (either with data or error)
    const certificateTypeCompleted = certificateData !== undefined || !activeCertificateId;
    const existingDataCompleted = existingData !== undefined || isError;

    if (certificateTypeCompleted && existingDataCompleted) {
      setIsLoading(false);
    }
  }, [existingData, certificateData, isError, activeCertificateId]);

  const saveMutation = useMutation({
    mutationFn: async (formData: ObjektdatenFormValues) => { // Renamed 'data' to 'formData' for clarity
      if (!activeCertificateId) throw new Error('Kein aktives Zertifikat ausgewählt.');

      // Create a copy of the form data to modify
      const dataToSave = { ...formData };

      // Auto-set WSchVo77_erfuellt to "1" (yes) for WG/V certificate type
      // Remove WSchVo77_erfuellt field if certificate type is not WG/V
      if (certificateType === 'WG/V') {
        dataToSave.WSchVo77_erfuellt = '1'; // Automatically set to "yes" for WG/V certificates
      } else {
        delete dataToSave.WSchVo77_erfuellt;
      }

      const { data: result, error } = await supabase
        .from('energieausweise')
        .update({
          objektdaten: dataToSave, // Use the modified form data
          updated_at: new Date().toISOString(),
        })
        .eq('id', activeCertificateId)
        .select() // it's good practice to select to get the written data back, if needed
        .single(); // if you expect one row back from upsert

      if (error) throw error;
      return result;
    },
    onSuccess: async () => {
      queryClient.invalidateQueries({ queryKey: ['energieausweise', 'objektdaten', activeCertificateId] });
      queryClient.invalidateQueries({ queryKey: ['energieausweise', activeCertificateId] });

      // Update navigation state to mark next page as current
      await markPageAsVisited('gebaeudedetails1');

      navigate({ to: '/erfassen/gebaeudedetails1' });
    },
    onError: (error) => {
      setSubmitError(`Fehler beim Speichern: ${error.message}`);
    },
  });

  const form = useForm({
    defaultValues: initialValues,
    onSubmit: async ({ value }) => {
      setSubmitError(null);
      setValidationErrors({});

      // Clear any existing field errors
      const fieldNames = Object.keys(objektdatenSchema.shape);
      clearFormFieldErrors(form, fieldNames);

      try {
        const validatedValues = objektdatenSchema.parse(value);
        saveMutation.mutate(validatedValues);
      } catch (validationError) {
        if (validationError instanceof z.ZodError) {
          // Use the improved validation error handling
          const { fieldErrors, summaryMessage } = handleZodValidationError(validationError, form);
          setValidationErrors(fieldErrors);
          setSubmitError(summaryMessage);

          // Log for debugging
          console.error("Form validation error:", validationError.flatten().fieldErrors);
        } else {
          setSubmitError("Ein unerwarteter Validierungsfehler ist aufgetreten.");
        }
      }
    },
  });

  // Update form values when data is loaded - use setFieldValue instead of form.reset to avoid re-rendering
  useEffect(() => {
    if (existingData && typeof existingData === 'object' && 'objektdaten' in existingData && existingData.objektdaten) {
      const data = existingData.objektdaten as ObjektdatenFormValues;
      // Set individual field values instead of resetting the entire form
      // Use a timeout to ensure this happens after the component is fully mounted
      const timeoutId = setTimeout(() => {
        Object.entries(data).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            form.setFieldValue(key as keyof ObjektdatenFormValues, value);
          }
        });
      }, 0);

      return () => clearTimeout(timeoutId);
    }
  }, [existingData]); // Only depend on existingData, not form to prevent re-runs


  // Read-only field component for displaying the order number
  const ReadOnlyOrderNumberField = () => {
    // Fetch the current certificate's order number
    const { data: certificateOrderData } = useQuery({
      queryKey: ['energieausweise', 'order_number', activeCertificateId],
      queryFn: async () => {
        if (!activeCertificateId) return null;

        const { data, error } = await supabase
          .from('energieausweise')
          .select('order_number')
          .eq('id', activeCertificateId)
          .single();

        if (error) {
          throw error;
        }
        return data;
      },
      enabled: !!activeCertificateId,
      retry: false,
    });

    const orderNumber = certificateOrderData?.order_number || `EA-${activeCertificateId?.slice(-8).toUpperCase()}`;

    return (
      <div className="mb-4">
        <label htmlFor="order_number" className="block text-sm font-medium text-gray-700 mb-1">
          Bestellnummer
        </label>
        <div className="flex items-center">
          <input
            id="order_number"
            name="order_number"
            type="text"
            value={orderNumber}
            readOnly
            className="w-full px-3 py-2 border border-gray-300 bg-gray-50 rounded-md shadow-sm focus:outline-none"
          />
        </div>
        <p className="mt-1 text-sm text-gray-500">
          Eine eindeutige Bestellnummer für dieses Energieausweis-Zertifikat
        </p>
      </div>
    );
  };



  return (
    <div className="max-w-5xl mx-auto px-2 sm:px-4 lg:px-6">
      {/* Breadcrumb Navigation */}
      <Breadcrumb className="mb-6" />

      <h1 className="text-3xl font-bold text-gray-800 mb-6">
        Objektdaten erfassen
      </h1>

      {/* Anonymous User Indicator */}
      <AnonymousUserIndicator className="mb-6" />

      <p className="text-lg text-gray-600 mb-8">
        Bitte geben Sie die allgemeinen Objektdaten und Kundendaten ein.
      </p>

      {isLoading ? (
        <div className="bg-white shadow-md rounded-lg p-6 flex justify-center items-center h-64">
          <div className="text-center">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-green-500 mb-2"></div>
            <p className="text-gray-600">Daten werden geladen...</p>
          </div>
        </div>
      ) : (
        <form
          onSubmit={(e) => {
            e.preventDefault();
            e.stopPropagation();
            form.handleSubmit();
          }}
          className="bg-white shadow-md rounded-lg p-6"
        >
       
        <div className="mb-8">
          <h2 className="text-xl font-semibold text-gray-800 mb-4 pb-2 border-b">
            Objektdaten
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="md:col-span-2">
              <ReadOnlyOrderNumberField />
            </div>
            <div className="md:col-span-2 grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="md:col-span-2">
                <EnhancedFormField
                  name="Strasse"
                  label="Straße"
                  placeholder="Straße des Objektes"
                  form={form}
                  helpText="Geben Sie die vollständige Straßenbezeichnung ein"
                />
              </div>
              <EnhancedFormField
                name="Hausnr"
                label="Hausnummer"
                placeholder="222"
                form={form}
              />
            </div>
            <EnhancedFormField
              name="PLZ"
              label="PLZ"
              placeholder="99423"
              form={form}
              helpText="5-stellige Postleitzahl"
            />
            <EnhancedFormField
              name="Ort"
              label="Ort"
              placeholder="Weimar"
              form={form}
            />

            <div className="md:col-span-2">
              <DirectoryBasedFileUpload
                certificateId={activeCertificateId}
                fieldName="gebaeudebild"
                label="Gebäudebild hochladen"
                accept=".jpg,.jpeg,.png,.webp"
                multiple={false}
                maxFiles={1}
                maxFileSize={5 * 1024 * 1024} // 5MB
                maxTotalSize={5 * 1024 * 1024} // 5MB
              />
            </div>
          </div>
        </div>

        <div className="mb-8">
          <h2 className="text-xl font-semibold text-gray-800 mb-4 pb-2 border-b">
            Kundendaten
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <EnhancedFormField
              name="Kunden_Anrede"
              label="Anrede"
              placeholder="Herr/Frau"
              form={form}
            />
            <div className="md:col-span-2 grid grid-cols-1 md:grid-cols-2 gap-4">
              <EnhancedFormField
                name="Kunden_Vorname"
                label="Vorname"
                placeholder="Max"
                form={form}
              />
              <EnhancedFormField
                name="Kunden_Nachname"
                label="Nachname"
                placeholder="Mustermann"
                form={form}
              />
            </div>
            <div className="md:col-span-2 grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="md:col-span-2">
                <EnhancedFormField
                  name="Kunden_Strasse"
                  label="Straße"
                  placeholder="Musterstr."
                  form={form}
                />
              </div>
              <EnhancedFormField
                name="Kunden_Hausnr"
                label="Hausnummer"
                placeholder="111"
                form={form}
              />
            </div>
            <EnhancedFormField
              name="Kunden_PLZ"
              label="PLZ"
              placeholder="99425"
              form={form}
              helpText="5-stellige Postleitzahl"
            />
            <EnhancedFormField
              name="Kunden_Ort"
              label="Ort"
              placeholder="Weimar"
              form={form}
            />
            <div className={isAnonymous ? "md:col-span-2" : ""}>
              <EnhancedFormField
                name="Kunden_email"
                label={isAnonymous ? "E-Mail-Adresse (erforderlich für Zustellung)" : "E-Mail"}
                type="email"
                placeholder="<EMAIL>"
                form={form}
                helpText={isAnonymous
                  ? "Diese E-Mail-Adresse wird für die Zustellung Ihres Energieausweises verwendet"
                  : "Gültige E-Mail-Adresse für Kommunikation"
                }
                autoComplete="email"
              />
              {isAnonymous && (
                <div className="mt-2 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <svg className="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div className="ml-3">
                      <p className="text-sm text-yellow-700">
                        <strong>Wichtig:</strong> Diese E-Mail-Adresse wird für die Zustellung Ihres fertigen
                        Energieausweises verwendet. Bitte stellen Sie sicher, dass sie korrekt ist.
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </div>
            <EnhancedFormField
              name="Kunden_telefon"
              label="Telefon"
              placeholder="03643/123456789"
              required={false}
              form={form}
              helpText="Optional: Telefonnummer für Rückfragen"
              autoComplete="tel"
            />
          </div>
        </div>


        {submitError && (
          <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
            {submitError}
          </div>
        )}

         {/* Validation Error Summary */}
        {Object.keys(validationErrors).length > 0 && (
          <ValidationErrorSummary errors={validationErrors} />
        )}

        <div className="flex justify-between mt-8">
          <Link
            to="/meine-zertifikate"
            className="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300 transition-colors"
          >
            Zurück
          </Link>
          <button
            type="submit"
            disabled={form.state.isSubmitting || saveMutation.isPending}
            className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors disabled:bg-green-300"
          >
            {form.state.isSubmitting || saveMutation.isPending ? 'Wird gespeichert...' : 'Weiter'}
          </button>
        </div>
      </form>
      )}

      {isError && (
        <div className="mt-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
          <p>Fehler beim Laden der Daten: {error instanceof Error ? error.message : 'Unbekannter Fehler'}</p>
          <p className="mt-2">Bitte versuchen Sie es später erneut oder kontaktieren Sie den Support.</p>
        </div>
      )}
    </div>
  );
};
